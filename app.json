{"expo": {"name": "Safqa Shipping", "slug": "safqa-shipping", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "safqashipping", "userInterfaceStyle": "automatic", "newArchEnabled": true, "splash": {"image": "./assets/images/splash-screen.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "ios": {"supportsTablet": true, "icon": "./assets/images/icon.png", "splash": {"image": "./assets/images/splash-screen.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "bundleIdentifier": "com.safqa.safqashipping"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "splash": {"image": "./assets/images/splash-screen.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "package": "com.safqa.safqashipping"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png", "splash": {"image": "./assets/images/splash-screen.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-screen.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}]], "experiments": {"typedRoutes": true}}}