{"name": "ExpoModulesCore", "version": "2.4.2", "summary": "The core of Expo Modules architecture", "description": "The core of Expo Modules architecture", "license": "MIT", "authors": "650 Industries, Inc.", "homepage": "https://github.com/expo/expo/tree/main/packages/expo-modules-core", "platforms": {"ios": "15.1", "osx": "11.0", "tvos": "15.1"}, "swift_versions": "5.4", "source": {"git": "https://github.com/expo/expo.git"}, "static_framework": true, "header_dir": "ExpoModulesCore", "pod_target_xcconfig": {"USE_HEADERMAP": "YES", "DEFINES_MODULE": "YES", "CLANG_CXX_LANGUAGE_STANDARD": "c++20", "SWIFT_COMPILATION_MODE": "wholemodule", "OTHER_SWIFT_FLAGS": "$(inherited) -DRCT_NEW_ARCH_ENABLED", "HEADER_SEARCH_PATHS": "\"${PODS_CONFIGURATION_BUILD_DIR}/React-jsinspector/jsinspector_modern.framework/Headers\" \"$(PODS_ROOT)/boost\" \"$(PODS_ROOT)/Headers/Private/Yoga\"", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) EXPO_MODULES_CORE_VERSION=2.4.2", "OTHER_CPLUSPLUSFLAGS": "$(inherited) -DRCT_NEW_ARCH_ENABLED=1 -DF<PERSON><PERSON>Y_NO_CONFIG -DF<PERSON>LY_MOBILE=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -Wno-comma -Wno-shorten-64-to-32"}, "user_target_xcconfig": {"HEADER_SEARCH_PATHS": ["\"${PODS_CONFIGURATION_BUILD_DIR}/ExpoModulesCore/Swift Compatibility Header\"", "\"$(PODS_ROOT)/Headers/Private/Yoga\""]}, "dependencies": {"hermes-engine": [], "React-jsinspector": [], "React-Core": [], "ReactCommon/turbomodule/core": [], "React-NativeModulesApple": [], "React-RCTFabric": [], "RCT-Folly": ["2024.11.18.00"], "glog": [], "ReactCodegen": [], "RCTRequired": [], "RCTTypeSafety": [], "ReactCommon/turbomodule/bridging": [], "Yoga": [], "React-Fabric": [], "React-graphics": [], "React-utils": [], "React-featureflags": [], "React-debug": [], "React-ImageManager": [], "React-rendererdebug": [], "DoubleConversion": [], "React-jsi": [], "React-renderercss": [], "React-hermes": []}, "compiler_flags": "-DFOLLY_NO_CONFIG -DF<PERSON><PERSON>Y_MOBILE=1 -DF<PERSON><PERSON>Y_USE_LIBCPP=1 -DF<PERSON>LY_CFG_NO_COROUTINES=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -Wno-comma -Wno-shorten-64-to-32 -DREACT_NATIVE_TARGET_VERSION=79 -DUSE_HERMES -DRCT_NEW_ARCH_ENABLED", "source_files": ["ios/**/*.{h,m,mm,swift,cpp}", "common/cpp/**/*.{h,cpp}"], "exclude_files": ["ios/Tests/"], "private_header_files": ["ios/**/*+Private.h", "ios/**/Swift.h"], "testspecs": [{"name": "Tests", "test_type": "unit", "dependencies": {"ExpoModulesTestCore": []}, "source_files": "ios/Tests/**/*.{m,swift}"}], "swift_version": "5.4"}