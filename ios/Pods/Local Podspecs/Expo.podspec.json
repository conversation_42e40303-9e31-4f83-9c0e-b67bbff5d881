{"name": "Expo", "version": "53.0.19", "summary": "The Expo SDK", "description": "The Expo SDK", "license": "MIT", "authors": "Expo", "homepage": "https://github.com/expo/expo/tree/main/packages/expo", "platforms": {"ios": "15.1", "osx": "11.0", "tvos": "15.1"}, "source": {"git": "https://github.com/expo/expo.git"}, "static_framework": true, "header_dir": "Expo", "dependencies": {"ExpoModulesCore": [], "React-RCTAppDelegate": [], "React-RCTFabric": [], "ReactAppDependencyProvider": [], "React-Core": [], "RCT-Folly": ["2024.11.18.00"], "glog": [], "ReactCodegen": [], "RCTRequired": [], "RCTTypeSafety": [], "ReactCommon/turbomodule/bridging": [], "ReactCommon/turbomodule/core": [], "React-NativeModulesApple": [], "Yoga": [], "React-Fabric": [], "React-graphics": [], "React-utils": [], "React-featureflags": [], "React-debug": [], "React-ImageManager": [], "React-rendererdebug": [], "DoubleConversion": [], "React-jsi": [], "React-renderercss": [], "hermes-engine": [], "React-hermes": []}, "pod_target_xcconfig": {"HEADER_SEARCH_PATHS": "\"$(PODS_ROOT)/Headers/Private/React-Core\" \"$(PODS_ROOT)/DoubleConversion\" \"${PODS_CONFIGURATION_BUILD_DIR}/ExpoModulesCore/Swift Compatibility Header\" \"$(PODS_ROOT)/boost\" \"$(PODS_ROOT)/Headers/Private/Yoga\"", "CLANG_CXX_LANGUAGE_STANDARD": "c++20", "OTHER_CPLUSPLUSFLAGS": "$(inherited) -DRCT_NEW_ARCH_ENABLED=1 -DF<PERSON><PERSON>Y_NO_CONFIG -DF<PERSON>LY_MOBILE=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -Wno-comma -Wno-shorten-64-to-32", "OTHER_SWIFT_FLAGS": "$(inherited) -DRCT_NEW_ARCH_ENABLED"}, "user_target_xcconfig": {"HEADER_SEARCH_PATHS": ["\"${PODS_CONFIGURATION_BUILD_DIR}/Expo/Swift Compatibility Header\"", "\"$(PODS_ROOT)/Headers/Private/Yoga\""]}, "compiler_flags": "-DFOLLY_NO_CONFIG -DF<PERSON>LY_MOBILE=1 -DF<PERSON>LY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -Wno-comma -Wno-shorten-64-to-32", "source_files": "ios/**/*.{h,m,mm,swift}", "private_header_files": ["ios/**/Swift.h"]}