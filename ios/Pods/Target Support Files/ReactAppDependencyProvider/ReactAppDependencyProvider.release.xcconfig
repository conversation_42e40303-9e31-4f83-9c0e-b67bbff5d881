CLANG_CXX_LANGUAGE_STANDARD = c++20
CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO
CONFIGURATION_BUILD_DIR = ${PODS_CONFIGURATION_BUILD_DIR}/ReactAppDependencyProvider
DEFINES_MODULE = YES
FRAMEWORK_SEARCH_PATHS = $(inherited) "${PODS_ROOT}/hermes-engine/destroot/Library/Frameworks/universal" "${PODS_XCFRAMEWORKS_BUILD_DIR}/hermes-engine/Pre-built"
GCC_PREPROCESSOR_DEFINITIONS = $(inherited) COCOAPODS=1
HEADER_SEARCH_PATHS = $(inherited) "${PODS_ROOT}/Headers/Private" "${PODS_ROOT}/Headers/Private/ReactAppDependencyProvider" "${PODS_ROOT}/Headers/Public" "${PODS_ROOT}/Headers/Public/DoubleConversion" "${PODS_ROOT}/Headers/Public/FBLazyVector" "${PODS_ROOT}/Headers/Public/RCT-Folly" "${PODS_ROOT}/Headers/Public/RCTRequired" "${PODS_ROOT}/Headers/Public/RCTTypeSafety" "${PODS_ROOT}/Headers/Public/React-Core" "${PODS_ROOT}/Headers/Public/React-Fabric" "${PODS_ROOT}/Headers/Public/React-FabricComponents" "${PODS_ROOT}/Headers/Public/React-FabricImage" "${PODS_ROOT}/Headers/Public/React-ImageManager" "${PODS_ROOT}/Headers/Public/React-Mapbuffer" "${PODS_ROOT}/Headers/Public/React-NativeModulesApple" "${PODS_ROOT}/Headers/Public/React-RCTAnimation" "${PODS_ROOT}/Headers/Public/React-RCTBlob" "${PODS_ROOT}/Headers/Public/React-RCTFBReactNativeSpec" "${PODS_ROOT}/Headers/Public/React-RCTFabric" "${PODS_ROOT}/Headers/Public/React-RCTRuntime" "${PODS_ROOT}/Headers/Public/React-RCTText" "${PODS_ROOT}/Headers/Public/React-RuntimeApple" "${PODS_ROOT}/Headers/Public/React-RuntimeCore" "${PODS_ROOT}/Headers/Public/React-RuntimeHermes" "${PODS_ROOT}/Headers/Public/React-callinvoker" "${PODS_ROOT}/Headers/Public/React-cxxreact" "${PODS_ROOT}/Headers/Public/React-debug" "${PODS_ROOT}/Headers/Public/React-defaultsnativemodule" "${PODS_ROOT}/Headers/Public/React-domnativemodule" "${PODS_ROOT}/Headers/Public/React-featureflags" "${PODS_ROOT}/Headers/Public/React-featureflagsnativemodule" "${PODS_ROOT}/Headers/Public/React-graphics" "${PODS_ROOT}/Headers/Public/React-hermes" "${PODS_ROOT}/Headers/Public/React-idlecallbacksnativemodule" "${PODS_ROOT}/Headers/Public/React-jserrorhandler" "${PODS_ROOT}/Headers/Public/React-jsi" "${PODS_ROOT}/Headers/Public/React-jsiexecutor" "${PODS_ROOT}/Headers/Public/React-jsinspector" "${PODS_ROOT}/Headers/Public/React-jsinspectortracing" "${PODS_ROOT}/Headers/Public/React-jsitooling" "${PODS_ROOT}/Headers/Public/React-logger" "${PODS_ROOT}/Headers/Public/React-microtasksnativemodule" "${PODS_ROOT}/Headers/Public/React-oscompat" "${PODS_ROOT}/Headers/Public/React-perflogger" "${PODS_ROOT}/Headers/Public/React-performancetimeline" "${PODS_ROOT}/Headers/Public/React-rendererconsistency" "${PODS_ROOT}/Headers/Public/React-renderercss" "${PODS_ROOT}/Headers/Public/React-rendererdebug" "${PODS_ROOT}/Headers/Public/React-runtimeexecutor" "${PODS_ROOT}/Headers/Public/React-runtimescheduler" "${PODS_ROOT}/Headers/Public/React-timing" "${PODS_ROOT}/Headers/Public/React-utils" "${PODS_ROOT}/Headers/Public/ReactCodegen" "${PODS_ROOT}/Headers/Public/ReactCommon" "${PODS_ROOT}/Headers/Public/Yoga" "${PODS_ROOT}/Headers/Public/boost" "${PODS_ROOT}/Headers/Public/fast_float" "${PODS_ROOT}/Headers/Public/fmt" "${PODS_ROOT}/Headers/Public/glog" "${PODS_ROOT}/Headers/Public/hermes-engine"
OTHER_CFLAGS = $(inherited) -fmodule-map-file="${PODS_ROOT}/Headers/Public/DoubleConversion/DoubleConversion.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/RCTDeprecation/RCTDeprecation.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/RCTFabric/React-RCTFabric.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/RCTRuntime/React-RCTRuntime.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/RCTTypeSafety/RCTTypeSafety.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/React/React-Core.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/ReactCodegen/ReactCodegen.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/ReactCommon/ReactCommon.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/React_Fabric/React-Fabric.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/React_FabricComponents/React-FabricComponents.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/React_NativeModulesApple/React-NativeModulesApple.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/React_RCTAppDelegate/React-RCTAppDelegate.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/folly/RCT-Folly.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/glog/glog.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/jsi/React-jsi.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/jsinspector_modern/React-jsinspector.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/jsinspector_modern_tracing/React-jsinspectortracing.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_debug/React-debug.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_featureflags/React-featureflags.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_nativemodule_defaults/React-defaultsnativemodule.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_nativemodule_dom/React-domnativemodule.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_nativemodule_featureflags/React-featureflagsnativemodule.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_nativemodule_idlecallbacks/React-idlecallbacksnativemodule.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_nativemodule_microtasks/React-microtasksnativemodule.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_renderer_css/React-renderercss.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_renderer_debug/React-rendererdebug.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_renderer_graphics/React-graphics.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_renderer_imagemanager/React-ImageManager.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_runtime/React-jsitooling.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/react_utils/React-utils.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/reacthermes/React-hermes.modulemap" -fmodule-map-file="${PODS_ROOT}/Headers/Public/yoga/Yoga.modulemap"
PODS_BUILD_DIR = ${BUILD_DIR}
PODS_CONFIGURATION_BUILD_DIR = ${PODS_BUILD_DIR}/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)
PODS_DEVELOPMENT_LANGUAGE = ${DEVELOPMENT_LANGUAGE}
PODS_ROOT = ${SRCROOT}
PODS_TARGET_SRCROOT = ${PODS_ROOT}/../build/generated/ios
PODS_XCFRAMEWORKS_BUILD_DIR = $(PODS_CONFIGURATION_BUILD_DIR)/XCFrameworkIntermediates
PRODUCT_BUNDLE_IDENTIFIER = org.cocoapods.${PRODUCT_NAME:rfc1034identifier}
SKIP_INSTALL = YES
USE_RECURSIVE_SCRIPT_INPUTS_IN_SCRIPT_PHASES = YES
