//  Boost utility.hpp header file  -------------------------------------------//

//  Copyright 1999-2003 Aleksey Gurtovoy.  Use, modification, and distribution are
//  subject to the Boost Software License, Version 1.0.  (See accompanying file
//  LICENSE_1_0.txt or a copy at <http://www.boost.org/LICENSE_1_0.txt>.)

//  See <http://www.boost.org/libs/utility/> for the library's home page.

#ifndef BOOST_UTILITY_HPP
#define BOOST_UTILITY_HPP

// Use of this header is discouraged and it will be deprecated.
// Please include one or more of the headers below instead.

#include <boost/utility/base_from_member.hpp>
#include <boost/utility/binary.hpp>
#include <boost/utility/identity_type.hpp>

#include <boost/core/addressof.hpp>
#include <boost/core/enable_if.hpp>
#include <boost/core/checked_delete.hpp>
#include <boost/core/noncopyable.hpp>

#endif  // BOOST_UTILITY_HPP
