This project has begun being ported to Windows.  A working solution
file exists in this directory:
    google-glog.sln

You can load this solution file into VC++ 9.0 (Visual Studio
2008).  You may also be able to use this solution file with older
Visual Studios by converting the solution file.

Note that stack tracing and some unittests are not ported
yet.

You can also link glog code in statically -- see the example project
libglog_static and logging_unittest_static, which does this.  For this
to work, you'll need to add "/D GOOGLE_GLOG_DLL_DECL=" to the compile
line of every glog's .cc file.

I have little experience with Windows programming, so there may be
better ways to set this up than I've done!  If you run across any
problems, please post to the google-glog Google Group, or report
them on the google-glog Google Code site:
   http://groups.google.com/group/google-glog
   https://github.com/google/glog/issues

-- <PERSON><PERSON><PERSON>

Last modified: 23 January 2009
