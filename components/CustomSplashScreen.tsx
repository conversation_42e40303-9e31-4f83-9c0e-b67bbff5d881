import React, { useEffect } from 'react';
import { Dimensions, Image, StyleSheet, View } from 'react-native';

const { width, height } = Dimensions.get('window');

interface CustomSplashScreenProps {
  onFinish: () => void;
  duration?: number;
}

export default function CustomSplashScreen({ onFinish, duration = 2000 }: CustomSplashScreenProps) {
  useEffect(() => {
    const timer = setTimeout(() => {
      onFinish();
    }, duration);

    return () => clearTimeout(timer);
  }, [onFinish, duration]);

  return (
    <View style={styles.container}>
      {/* Option 1: Use ImageBackground for pattern */}
      {/*
      <ImageBackground
        source={require('../assets/images/pattern-bg.png')}
        style={styles.backgroundPattern}
        resizeMode="repeat"
      >
      */}

      {/* Option 2: Simple background with overlay pattern */}
      <View style={styles.backgroundPattern}>
        {/* Create a simple dot pattern */}
        {Array.from({ length: 20 }).map((_, i) => (
          <View key={i} style={[styles.patternDot, {
            top: (i % 4) * (height / 4) + 50,
            left: Math.floor(i / 4) * (width / 5) + 30,
          }]} />
        ))}
      </View>

      {/* Centered Logo */}
      <View style={styles.logoContainer}>
        <Image
          source={require('../assets/images/splash-screen.png')}
          style={styles.logo}
          resizeMode="contain"
        />
      </View>

      {/* Close ImageBackground if using Option 1 */}
      {/* </ImageBackground> */}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
    justifyContent: 'center',
    alignItems: 'center',
  },
  backgroundPattern: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: '#ffffff',
  },
  patternDot: {
    position: 'absolute',
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: '#f0f0f0',
    opacity: 0.5,
  },
  logoContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    width: width * 0.6, // 60% of screen width
    height: height * 0.3, // 30% of screen height
  },
  logo: {
    width: '100%',
    height: '100%',
    maxWidth: 300,
    maxHeight: 200,
  },
});
