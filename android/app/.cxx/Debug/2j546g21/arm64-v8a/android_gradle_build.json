{"buildFiles": ["/Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/CMakeLists.txt", "/Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/CMakeLists.txt", "/Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/CMakeLists.txt", "/Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt", "/Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt", "/Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/react-native-webview/android/build/generated/source/codegen/jni/CMakeLists.txt", "/Users/<USER>/Desktop/react-native/SafqaShipping/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Desktop/react-native/SafqaShipping/android/app/.cxx/Debug/2j546g21/arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Desktop/react-native/SafqaShipping/android/app/.cxx/Debug/2j546g21/arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"appmodules::@6890427a1f51a3e7e1df": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "appmodules", "output": "/Users/<USER>/Desktop/react-native/SafqaShipping/android/app/build/intermediates/cxx/Debug/2j546g21/obj/arm64-v8a/libappmodules.so", "runtimeFiles": ["/Users/<USER>/Desktop/react-native/SafqaShipping/android/app/build/intermediates/cxx/Debug/2j546g21/obj/arm64-v8a/libreact_codegen_safeareacontext.so", "/Users/<USER>/Desktop/react-native/SafqaShipping/android/app/build/intermediates/cxx/Debug/2j546g21/obj/arm64-v8a/libreact_codegen_rnscreens.so", "/Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so", "/Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so", "/Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so"]}, "react_codegen_RNCWebViewSpec::@eb48929f9f7453740a6c": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "react_codegen_RNCWebViewSpec"}, "react_codegen_RNEdgeToEdge::@668218a48b60eb28aa9e": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "react_codegen_RNEdgeToEdge"}, "react_codegen_rngesturehandler_codegen::@39f233abcd2c728bc6ec": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "react_codegen_rngesturehandler_codegen"}, "react_codegen_rnreanimated::@8afabad14bfffa3f8b9a": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "react_codegen_rnreanimated"}, "react_codegen_rnscreens::@25bcbd507e98d3a854ad": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "react_codegen_rnscreens", "output": "/Users/<USER>/Desktop/react-native/SafqaShipping/android/app/build/intermediates/cxx/Debug/2j546g21/obj/arm64-v8a/libreact_codegen_rnscreens.so", "runtimeFiles": ["/Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so", "/Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so", "/Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so"]}, "react_codegen_safeareacontext::@7984cd80db47aa7b952a": {"toolchain": "toolchain", "abi": "arm64-v8a", "artifactName": "react_codegen_safeareacontext", "output": "/Users/<USER>/Desktop/react-native/SafqaShipping/android/app/build/intermediates/cxx/Debug/2j546g21/obj/arm64-v8a/libreact_codegen_safeareacontext.so", "runtimeFiles": ["/Users/<USER>/.gradle/caches/8.13/transforms/492d12ddde8d0414cdeaa8d1c9a89b37/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so", "/Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so", "/Users/<USER>/.gradle/caches/8.13/transforms/ecf58a1d5a3b5cc4faabc13e873181f2/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so"]}}, "toolchains": {"toolchain": {"cCompilerExecutable": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang.lld", "cppCompilerExecutable": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++.lld"}}, "cFileExtensions": [], "cppFileExtensions": ["cpp"]}